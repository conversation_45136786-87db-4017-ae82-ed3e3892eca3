{% layout '' %}
<script src="/assets/js/common.js"></script>
<script>
    {{ Model.Js | raw }}
</script>
<!-- 方式1：使用ViewData中的JSON数据解析VisualPageData中的header配置 -->
{% assign visualPageData = ViewData.VisualPageData | jsonparse %}
{% assign headerConfig = null %}
{% assign showHeader = true %}
{% assign showSearch = true %}
{% assign showUser = true %}
{% assign showShoppingCart = true %}
{% assign showLanguageSwitch = true %}
{% assign showMenu = true %}
{% assign logoConfig = null %}
{% assign searchPlaceholder = null %}
{% assign headerStyle = "default" %}
{% assign headerBackgroundColor = null %}
{% assign headerTextColor = null %}

<!-- 解析Header插件配置 -->
{% if visualPageData and visualPageData.PluginsByType and visualPageData.PluginsByType.header %}
    {% assign headerPlugins = visualPageData.PluginsByType.header %}
    {% if headerPlugins.size > 0 %}
        {% assign headerPlugin = headerPlugins[0] %}

        <!-- 解析显示配置 - Config是JSON字符串，需要解析 -->
        {% if headerPlugin.Config %}
            {% assign configData = headerPlugin.Config | jsonparse %}
            {% assign displayValue = configData.Display | append: "" %}
            {% if displayValue == "0" %}
                {% assign showHeader = false %}
            {% endif %}

            {% if configData.Style %}
                {% assign headerStyle = configData.Style %}
            {% endif %}

            {% if configData.BackgroundColor %}
                {% assign headerBackgroundColor = configData.BackgroundColor %}
            {% endif %}

            {% if configData.TextColor %}
                {% assign headerTextColor = configData.TextColor %}
            {% endif %}
        {% endif %}

        <!-- 解析功能设置 - Settings是JSON字符串，需要解析 -->
        {% if headerPlugin.Settings %}
            {% assign settingsData = headerPlugin.Settings | jsonparse %}
            {% assign searchValue = settingsData.Search | append: "" %}
            {% if searchValue == "0" %}
                {% assign showSearch = false %}
            {% endif %}

            {% assign userValue = settingsData.User | append: "" %}
            {% if userValue == "0" %}
                {% assign showUser = false %}
            {% endif %}

            {% assign shoppingCartValue = settingsData.ShoppingCart | append: "" %}
            {% if shoppingCartValue == "0" %}
                {% assign showShoppingCart = false %}
            {% endif %}

            {% assign languageSwitchValue = settingsData.LanguageSwitch | append: "" %}
            {% if languageSwitchValue == "0" %}
                {% assign showLanguageSwitch = false %}
            {% endif %}

            {% if settingsData.SearchPlaceholder %}
                {% assign searchPlaceholder = settingsData.SearchPlaceholder %}
            {% endif %}
        {% endif %}

        <!-- 解析块配置 - Blocks是JSON字符串，需要解析 -->
        {% if headerPlugin.Blocks %}
            {% assign blocksData = headerPlugin.Blocks | jsonparse %}
            {% if blocksData.Logo %}
                {% assign logoConfig = blocksData.Logo %}
            {% endif %}

            {% if blocksData.Menu %}
                {% assign menuValue = blocksData.Menu.Menu | append: "" %}
                {% if menuValue == "0" %}
                    {% assign showMenu = false %}
                {% endif %}
            {% endif %}
        {% endif %}
    {% endif %}
{% endif %}

<!--调试信息 - 方式1：JSON解析方式-->
<div style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc; font-family: monospace; font-size: 12px;">
    <h4>Header配置调试信息 (方式1：JSON解析):</h4>
    <p><strong>原始JSON数据:</strong> {{ ViewData.VisualPageData }}</p>
    <p><strong>JSON解析成功:</strong> {% if visualPageData %}是{% else %}否{% endif %}</p>
    <p><strong>PluginsByType存在:</strong> {% if visualPageData.PluginsByType %}是{% else %}否{% endif %}</p>
    {% if visualPageData and visualPageData.PluginsByType and visualPageData.PluginsByType.header %}
        <p><strong>Header插件数量:</strong> {{ visualPageData.PluginsByType.header.size }}</p>
        {% if visualPageData.PluginsByType.header.size > 0 %}
            {% assign headerPlugin = visualPageData.PluginsByType.header[0] %}
            {% assign configData = headerPlugin.Config | jsonparse %}
            {% assign settingsData = headerPlugin.Settings | jsonparse %}
            {% assign blocksData = headerPlugin.Blocks | jsonparse %}

            <p><strong>Header插件配置:</strong></p>
            <ul>
                <li>PId: {{ headerPlugin.PId }}</li>
                <li>Type: {{ headerPlugin.Type }}</li>
                <li>Mode: {{ headerPlugin.Mode }}</li>
            </ul>

            <p><strong>原始JSON字符串:</strong></p>
            <ul>
                <li>Config: {{ headerPlugin.Config }}</li>
                <li>Settings: {{ headerPlugin.Settings }}</li>
                <li>Blocks: {{ headerPlugin.Blocks }}</li>
            </ul>

            <p><strong>解析后的配置:</strong></p>
            <ul>
                <li>Display: {{ configData.Display }} → 转换后: "{{ configData.Display | append: "" }}"</li>
                <li>Style: {{ configData.Style }}</li>
                <li>BackgroundColor: {{ configData.BackgroundColor }}</li>
                <li>TextColor: {{ configData.TextColor }}</li>
                <li>Search: {{ settingsData.Search }} → 转换后: "{{ settingsData.Search | append: "" }}"</li>
                <li>User: {{ settingsData.User }} → 转换后: "{{ settingsData.User | append: "" }}"</li>
                <li>ShoppingCart: {{ settingsData.ShoppingCart }} → 转换后: "{{ settingsData.ShoppingCart | append: "" }}"</li>
                <li>LanguageSwitch: {{ settingsData.LanguageSwitch }} → 转换后: "{{ settingsData.LanguageSwitch | append: "" }}"</li>
                <li>SearchPlaceholder: {{ settingsData.SearchPlaceholder }}</li>
                <li>Menu: {{ blocksData.Menu.Menu }} → 转换后: "{{ blocksData.Menu.Menu | append: "" }}"</li>
                <li>Logo: {% if blocksData.Logo.Logo %}{{ blocksData.Logo.Logo }}{% else %}无{% endif %}</li>
                <li>LogoWidth: {{ blocksData.Logo.LogoWidth }}</li>
                <li>MobileLogoWidth: {{ blocksData.Logo.MobileLogoWidth }}</li>
                <li>ImageAlt: {{ blocksData.Logo.ImageAlt }}</li>
            </ul>
        {% endif %}
    {% else %}
        <p><strong>未找到Header插件</strong></p>
    {% endif %}
    <p><strong>当前配置状态:</strong></p>
    <ul>
        <li>showHeader: {{ showHeader }}</li>
        <li>showSearch: {{ showSearch }}</li>
        <li>showUser: {{ showUser }}</li>
        <li>showShoppingCart: {{ showShoppingCart }}</li>
        <li>showLanguageSwitch: {{ showLanguageSwitch }}</li>
        <li>showMenu: {{ showMenu }}</li>
        <li>headerStyle: {{ headerStyle }}</li>
        <li>headerBackgroundColor: {{ headerBackgroundColor }}</li>
        <li>headerTextColor: {{ headerTextColor }}</li>
        <li>logoConfig: {% if logoConfig %}有配置{% else %}无配置{% endif %}</li>
        <li>searchPlaceholder: {{ searchPlaceholder }}</li>
    </ul>
</div>

<!--Marquee Text-->
<div id="head_activities"></div>
<!--<div class="topbar-slider clearfix" style=" background: var(--theme-color); padding: 10px 0;">
    <div class="container-fluid">
        <div class="marquee-text">
            <div class="top-info-bar d-flex">
                <div class="flex-item center"><a href="#;"><span class="flash-icon">⚡</span>T130P – Limited Quantity Remaining! Order Now >></a></div>
                <div class="flex-item center"><a href="#;"><b><span class="flash-icon">🎧</span>Tour Guide System - Get Free Demo Kit >></a></div>
            </div>
        </div>
    </div>
</div>-->
<!--End Marquee Text-->
<!--Header-->
{% if showHeader %}
<div class="topheader">
    <div class="container-fluid">
        <div class="row">
            <div class="col-10 col-sm-8 col-md-5 col-lg-4">
                <p class="email"><a href=""><i style="font-size:20px;" class="icon an an-envelope"></i></a></p>
                <p class="phone-no mx-4"><a href=""><i style="font-size:18px;" class="an an-phone"></i></a></p>
                <p class="phone-no"><a style="color: var(--theme-color);font-weight: bold;"
                                       href="/pages/conviertase-en-distribuidor">Become a Dealer</a></p>
            </div>
            <div class="col-sm-4 col-md-5 col-lg-4 d-none d-md-block d-lg-block">
            </div>
            <div class="col-2 col-sm-4 col-md-2 col-lg-4 text-right">
            </div>
        </div>
    </div>
</div>
<header class="header d-flex align-items-center header-7 header-sticky {{ headerStyle }}"
        {% if headerBackgroundColor or headerTextColor %}
        style="{% if headerBackgroundColor %}background-color: {{ headerBackgroundColor }};{% endif %}{% if headerTextColor %}color: {{ headerTextColor }};{% endif %}"
        {% endif %}>
    <div class="container-fluid">
        <div class="row">
            <!--Mobile Icons-->
            <div class="col-4 col-sm-4 col-md-4 d-block d-lg-none mobile-icons">
                <!--Mobile Toggle-->
                <button type="button" class="btn--link site-header__menu js-mobile-nav-toggle mobile-nav--open">
                    <i class="icon an an-times"></i>
                    <i class="an an-bars"></i>
                </button>
                <!--End Mobile Toggle-->
                <!--Search-->
                {% if showSearch %}
                <div class="site-search iconset">
                    <i class="icon an an-search"></i>
                </div>
                {% endif %}
                <!--End Search-->
            </div>
            <!--Mobile Icons-->
            <!--Desktop Logo-->
            <div class="logo col-4 col-sm-4 col-md-4 col-lg-3 align-self-center">
                <a href="/">
                    {% if logoConfig and logoConfig.Logo %}
                        <img src="{{ logoConfig.Logo }}"
                             alt="{% if logoConfig.ImageAlt %}{{ logoConfig.ImageAlt }}{% else %}Logo{% endif %}"
                             title="{% if logoConfig.ImageAlt %}{{ logoConfig.ImageAlt }}{% else %}Logo{% endif %}"
                             class="logo-img"
                             {% if logoConfig.LogoWidth or logoConfig.MobileLogoWidth %}
                             style="{% if logoConfig.LogoWidth %}width: {{ logoConfig.LogoWidth }};{% endif %}{% if logoConfig.MobileLogoWidth %} --mobile-logo-width: {{ logoConfig.MobileLogoWidth }};{% endif %}"
                             {% endif %}/>
                    {% else %}
                        <img src="{{ static_path }}/assets/images/logo/retekess-logo.png" alt="Retekess" title="Retekess" class="logo-img"/>
                    {% endif %}
                </a>
            </div>
            <!--End Desktop Logo-->
            <div class="col-1 col-sm-1 col-md-1 col-lg-6 align-self-center d-menu-col">
                <!--Desktop Menu - 通过HTMX加载-->
                {% if showMenu %}
                <div id="desktop-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select="nav#AccessibleNav">
                    <!-- 导航菜单将通过HTMX加载 -->
                </div>
                {% endif %}
                <!--End Desktop Menu-->
            </div>
            <div class="col-4 col-sm-4 col-md-4 col-lg-3 align-self-center icons-col text-right">
                <!--Search-->
                {% if showSearch %}
                <div class="site-search iconset">
                    <i class="icon an an-search"></i>
                </div>
                {% endif %}
                {% if showSearch %}
                <div class="search-drawer">
                    <div class="container">
                        <div class="block block-search">
                            <div class="block block-content">
                                <div class="searchField">
                                    <div class="input-box">
                                        <input type="text" name="q" value=""
                                               placeholder="{% if searchPlaceholder %}{{ searchPlaceholder }}{% else %}{{ "blog.global.searchBtn" | translate }}...{% endif %}"
                                               class="input-text">
                                        <button type="submit" title="{{ "blog.global.searchBtn" | translate }}"
                                                class="action search" disabled=""><i class="icon an an-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                <!--End Search-->
                <!--Setting Dropdown-->
                {% if showUser %}
                <div class="setting-link iconset">
                    <i class="icon an an-cog"></i>
                </div>
                {% endif %}
                {% if showUser %}
                <div id="settingsBox">
                    <div class="customer-links">
                        {% if IsLogined == "true" %}
                            <!-- 已登录 -->
                            <div class="dropdown-menu" style="display:block;position:relative;border:none;">
                                <a class="dropdown-item"
                                   href="/Account/MyProfile">{{ "user.account.indexTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyOrders">{{ "user.account.orderTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyInbox">{{ "user.account.inboxTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyAddress">{{ "user.account.addressTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyCoupon">{{ "user.account.couponTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyWishList">{{ "user.account.favoriteTitle" | translate }}</a>
                                <!--<a class="dropdown-item"
                                   href="/Account/MyReview">{{ "user.account.reviewTitle" | translate }}</a>-->
                                <a class="dropdown-item"
                                   href="/account/SignOut">{{ "user.account.logOut" | translate }}</a>
                            </div>
                        {% else %}
                            <!-- 未登录 -->
                            <p><a href="/account/signin"
                                  class="btn theme-btn">{{ "user.global.sign_in" | translate }}</a></p>
                            <p class="text-center"><a href="/account/signup"
                                                      class="register">{{ "user.register.register_title" | translate }}</a>
                            </p>
                        {% endif %}

                    </div>

                    {% if showLanguageSwitch %}
                    <div hx-get="/home/<USER>" hx-trigger="load"></div>
                    {% endif %}

                </div>
                {% endif %}
                <!--End Setting Dropdown-->
                <!--Wishlist-->
                <div class="wishlist-link iconset">
                    <a href="/Account/MyWishList">
                        <i class="icon an an-heart-o"></i>
                        <span class="wishlist-count">0</span>
                    </a>
                </div>
                <!--End Wishlist-->
                <!--Minicart Dropdown-->
                {% if showShoppingCart %}
                <div class="header-cart iconset">
                    <a href="/cart" class="site-header__cart btn-minicart">
                        <div class="icon-in">
                            <i class="icon an an-shopping-cart"></i>
                            <span class="site-cart-count">0</span>
                        </div>
                    </a>
                </div>
                {% endif %}
                <!--End Minicart Dropdown-->
            </div>
        </div>
    </div>
</header>
{% endif %}
<!--End Header-->
<!--Mobile Menu - 通过HTMX加载-->
{% if showMenu %}
<div id="mobile-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select=".mobile-nav-wrapper">
    <!-- 移动端导航菜单将通过HTMX加载 -->
</div>
{% endif %}
<!--End Mobile Menu-->
<!--MiniCart Drawer-->
<!--{% assign miniCart = '/Themes/' | append: theme | append: '/Order/MiniCartDrawer' %}
    {% include miniCart -%}-->
<!--End MiniCart Drawer-->
<input type="hidden" name="operateactivity" id="operateactivity" value="{{ Model.OperateData }}"/>
<script src="/assets/js/operateactivity.js"></script>
<!-- 全局搜索功能脚本 -->
<script>
    // t200主题全局搜索功能实现
    function initGlobalSearchT200() {
        // 获取搜索框和搜索按钮
        const searchInput = document.querySelector('.search-drawer input[name="q"]');
        const searchButton = document.querySelector('.search-drawer .action.search');

        if (!searchInput || !searchButton) {
            console.log('t200搜索元素未找到');
            return;
        }

        // 搜索函数
        function performSearch() {
            const keyword = searchInput.value.trim();
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }

            // 构建搜索URL - 始终跳转到/shop页面
            let url = '/collections';
            let params = [];

            // 添加keyword参数
            params.push(`keyword=${encodeURIComponent(keyword)}`);

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            // 关闭搜索抽屉
            document.querySelector('.search-drawer').classList.remove('search-drawer-open');
            document.querySelector('.mask-overlay')?.remove();

            // 跳转到搜索结果页面
            window.location.href = url;
        }

        // 移除按钮的disabled属性
        searchButton.removeAttribute('disabled');

        // 绑定搜索按钮点击事件
        searchButton.addEventListener('click', function (e) {
            e.preventDefault();
            performSearch();
        });

        // 绑定回车键事件
        searchInput.addEventListener('keydown', function (e) {
            if (e.key === 'Enter' || e.keyCode === 13) {
                e.preventDefault();
                performSearch();
            }
        });

    }

    // 页面加载完成后初始化搜索功能
    document.addEventListener('DOMContentLoaded', function () {
        initGlobalSearchT200();
    });

    // 如果是通过HTMX加载的页面，也需要初始化
    document.addEventListener('htmx:afterSwap', function () {
        initGlobalSearchT200();
    });

    $(function () {

        //获取用户配置
        GetUserConf();

        // 方式1：使用JSON解析的VisualPageData
        initHeaderWithJsonData();

    })

    // 使用方式1：JSON解析方式获取Header配置
    function initHeaderWithJsonData() {
        try {
            // 获取自动注入的VisualPageData JSON数据
            const visualPageData = {{ ViewData.VisualPageData | raw }};

            console.log('方式1 - JSON解析的VisualPageData:', visualPageData);

            // 在JavaScript中使用Header配置
            if (visualPageData && visualPageData.PluginsByType && visualPageData.PluginsByType.header) {
                const headerPlugins = visualPageData.PluginsByType.header;
                if (headerPlugins.length > 0) {
                    const headerPlugin = headerPlugins[0];

                    console.log('Header插件原始数据:', headerPlugin);

                    // 解析嵌套的JSON字符串
                    let configData = null;
                    let settingsData = null;
                    let blocksData = null;

                    try {
                        if (headerPlugin.Config) {
                            configData = JSON.parse(headerPlugin.Config);
                            console.log('Config解析结果:', configData);
                        }

                        if (headerPlugin.Settings) {
                            settingsData = JSON.parse(headerPlugin.Settings);
                            console.log('Settings解析结果:', settingsData);
                        }

                        if (headerPlugin.Blocks) {
                            blocksData = JSON.parse(headerPlugin.Blocks);
                            console.log('Blocks解析结果:', blocksData);
                        }
                    } catch (parseError) {
                        console.error('解析嵌套JSON时出错:', parseError);
                        return;
                    }

                    // 使用解析后的数据
                    if (settingsData) {
                        // 例如：动态设置搜索功能
                        if (settingsData.Search === "1") {
                            console.log('✅ 搜索功能已启用 (方式1)');
                            // 可以在这里添加搜索相关的JavaScript逻辑
                        }

                        // 例如：动态设置购物车功能
                        if (settingsData.ShoppingCart === "1") {
                            console.log('✅ 购物车功能已启用 (方式1)');
                            // 可以在这里添加购物车相关的JavaScript逻辑
                        }

                        // 搜索占位符
                        if (settingsData.SearchPlaceholder) {
                            console.log('🔍 搜索占位符:', settingsData.SearchPlaceholder);
                        }
                    }

                    // 例如：动态设置样式
                    if (configData) {
                        if (configData.BackgroundColor) {
                            console.log('🎨 Header背景色:', configData.BackgroundColor);
                        }
                        if (configData.TextColor) {
                            console.log('🎨 Header文字色:', configData.TextColor);
                        }
                        if (configData.Style) {
                            console.log('🎨 Header样式:', configData.Style);
                        }
                    }

                    // 例如：Logo配置
                    if (blocksData && blocksData.Logo) {
                        const logoConfig = blocksData.Logo;
                        console.log('🖼️ Logo配置:', logoConfig);
                        if (logoConfig.Logo) {
                            console.log('🖼️ Logo图片:', logoConfig.Logo);
                        }
                        if (logoConfig.LogoWidth) {
                            console.log('📏 Logo宽度:', logoConfig.LogoWidth);
                        }
                        if (logoConfig.MobileLogoWidth) {
                            console.log('📱 移动端Logo宽度:', logoConfig.MobileLogoWidth);
                        }
                    }
                }
            } else {
                console.log('❌ 未找到Header插件配置 (方式1)');
            }
        } catch (error) {
            console.error('❌ 解析VisualPageData JSON数据时出错:', error);
        }
    }

    //获取用户配置
    function GetUserConf() {
        $.ajax({
            url: '/Account/GetUserConf',
            method: 'GET',
            contentType: 'application/json',
            success: function (data) {

                $(".wishlist-count").text(data.wishlistCount);
                $(".site-cart-count").text(data.cartCount);
                $(".cart-count").text(data.cartCount);

            },
            error: function (xhr, status, error) {
                // 关闭加载提示
                customize_pop.loadingClose();
                customize_pop.error('Please try again later.', null, null, {showIcon: true});
            },
            complete: function () {
                // 可以在这里添加完成后的处理逻辑
            }
        });
    }

</script>

<!-- 动态Logo样式 -->
{% if logoConfig and logoConfig.MobileLogoWidth %}
<style>
    @media (max-width: 991px) {
        .logo-img {
            width: {{ logoConfig.MobileLogoWidth }} !important;
        }
    }
</style>
{% endif %}

{% comment %}
=== Header.liquid 使用 VisualPageData 的两种方式说明 ===

方式1：JSON解析方式 (当前使用)
{% assign visualPageData = ViewData.VisualPageData | jsonparse %}
- 优点：数据格式标准化，便于调试和查看原始JSON
- 缺点：需要JSON解析步骤，性能略低
- 适用场景：需要在JavaScript中使用相同数据，或需要调试JSON格式

方式2：直接对象方式 (之前使用)
{% assign visualPageData = ViewData.VisualPageDataObject %}
- 优点：直接访问对象属性，性能更好
- 缺点：无法查看原始JSON格式，调试相对困难
- 适用场景：纯Liquid模板使用，不需要JavaScript交互

两种方式的数据结构完全相同，可以根据具体需求选择：
- 如果需要在JavaScript中使用数据，推荐方式1
- 如果只在Liquid模板中使用，推荐方式2

当前实现展示了方式1的完整用法，包括：
1. Liquid模板中的数据解析和使用
2. JavaScript中的数据访问和处理
3. 动态样式和配置的应用
4. 完整的调试信息输出
{% endcomment %}